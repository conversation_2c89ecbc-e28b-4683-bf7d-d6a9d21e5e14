# Event Feedback System

This document describes the comprehensive feedback system implemented for the event platform.

## Overview

The feedback system automatically collects attendee feedback after events and integrates the data into AI-generated reports. It includes:

- Automated email scheduling and sending
- Customizable feedback forms
- Real-time analytics dashboard
- Integration with AI report generation
- Anonymous feedback options

## Features

### 1. Event Creation Integration
- **Feedback Toggle**: Enable/disable feedback collection per event
- **Timing Configuration**: Set when feedback emails should be sent (1 hour to 1 week after event)
- **Custom Questions**: Add event-specific questions beyond default ones

### 2. Default Feedback Questions
- Overall satisfaction (1-5 stars)
- Content quality rating (1-5 stars)
- Organization rating (1-5 stars)
- Venue rating (1-5 stars, for physical events)
- Recommendation score (1-10, NPS style)
- What did you like most? (text)
- What could be improved? (text)
- Additional comments (text)

### 3. Custom Question Types
- **Rating**: 1-5 star ratings
- **Text**: Open-ended text responses
- **Multiple Choice**: Select from predefined options
- **Yes/No**: Simple binary choice

### 4. Email System
- **Professional Templates**: Mobile-responsive HTML email templates
- **Automated Scheduling**: Emails sent automatically based on event settings
- **Bulk Processing**: Efficient handling of multiple attendees
- **Retry Logic**: Automatic retry for failed email deliveries

### 5. Feedback Form
- **Mobile-Responsive**: Works perfectly on all devices
- **Progressive Enhancement**: Smooth user experience with visual feedback
- **Anonymous Option**: Users can choose to submit feedback anonymously
- **Validation**: Client and server-side validation

### 6. Analytics Dashboard
- **Response Metrics**: Total responses, response rate
- **Rating Analytics**: Average ratings across all categories
- **NPS Calculation**: Net Promoter Score with categorization
- **Individual Responses**: Detailed view of each feedback submission
- **Real-time Updates**: Live data with refresh capability

### 7. AI Report Integration
- **Feedback Metrics**: Automatically included in AI-generated reports
- **Satisfaction Analysis**: Detailed breakdown of attendee satisfaction
- **Recommendation Insights**: NPS analysis and recommendations
- **Trend Analysis**: Comparison with attendance and financial metrics

## Technical Implementation

### Database Models

#### FeedbackTemplate
```typescript
{
  event: ObjectId,
  customQuestions: [CustomQuestion],
  feedbackHours: Number,
  isActive: Boolean
}
```

#### FeedbackResponse
```typescript
{
  event: ObjectId,
  user: ObjectId (optional),
  isAnonymous: Boolean,
  overallSatisfaction: Number,
  contentQuality: Number,
  organizationRating: Number,
  venueRating: Number,
  recommendationScore: Number,
  likedMost: String,
  improvements: String,
  additionalComments: String,
  customAnswers: [FeedbackAnswer],
  submittedAt: Date
}
```

#### EmailSchedule
```typescript
{
  event: ObjectId,
  scheduledFor: Date,
  emailType: String,
  status: String,
  sentAt: Date,
  failureReason: String,
  retryCount: Number
}
```

### API Endpoints

- `GET /api/feedback/template/[eventId]` - Get feedback template
- `POST /api/feedback/submit` - Submit feedback response
- `GET /api/feedback/responses/[eventId]` - Get responses (organizers only)
- `POST /api/cron/feedback-emails` - Process scheduled emails

### Email Scheduling

The system uses a cron job approach:
1. When an event is created, an email schedule is created
2. A cron job runs periodically to check for due emails
3. Emails are sent to all event attendees
4. Failed emails are retried with exponential backoff

## Setup Instructions

### 1. Environment Variables
Add to your `.env.local`:
```bash
RESEND_API_KEY=your_resend_api_key
NEXT_PUBLIC_BASE_URL=your_domain_url
CRON_SECRET=optional_cron_security_token
```

### 2. Resend API Setup
1. Sign up at [Resend.com](https://resend.com)
2. Get your API key
3. Verify your domain for email sending
4. Update the `from` email addresses in `lib/email/resend.ts`

### 3. Cron Job Setup
Set up a cron job to call the feedback email endpoint:
```bash
# Every 30 minutes
*/30 * * * * curl -X POST https://yourdomain.com/api/cron/feedback-emails \
  -H "Authorization: Bearer your_cron_secret"
```

For Vercel deployment, you can use Vercel Cron:
```json
// vercel.json
{
  "crons": [
    {
      "path": "/api/cron/feedback-emails",
      "schedule": "*/30 * * * *"
    }
  ]
}
```

### 4. Email Template Customization
Update the email template in `lib/email/resend.ts`:
- Change branding and colors
- Update domain and contact information
- Customize messaging and tone

## Usage Guide

### For Event Organizers

1. **Creating Events**: 
   - Enable feedback collection in the event creation form
   - Set when feedback emails should be sent
   - Add custom questions if needed

2. **Viewing Feedback**:
   - Access the feedback dashboard from your event management page
   - View analytics and individual responses
   - Export data for further analysis

3. **AI Reports**:
   - Generate reports that automatically include feedback metrics
   - Use feedback insights for future event planning

### For Attendees

1. **Receiving Emails**:
   - Feedback emails are sent automatically after events
   - Click the feedback link to access the form

2. **Submitting Feedback**:
   - Complete the feedback form (takes ~3 minutes)
   - Choose to submit anonymously if preferred
   - Receive confirmation upon submission

## Security & Privacy

- **Authentication**: Feedback submission requires valid session or allows anonymous
- **Authorization**: Only event organizers can view feedback responses
- **Privacy**: Anonymous feedback option protects user identity
- **Rate Limiting**: Prevents spam and abuse
- **Data Validation**: Server-side validation ensures data integrity

## Performance Considerations

- **Pagination**: Large feedback datasets are paginated
- **Caching**: Analytics are cached for better performance
- **Bulk Operations**: Email sending is optimized for large attendee lists
- **Database Indexing**: Proper indexes for fast queries

## Monitoring & Troubleshooting

### Common Issues

1. **Emails Not Sending**:
   - Check Resend API key and domain verification
   - Verify cron job is running
   - Check email schedule status in database

2. **Feedback Form Not Loading**:
   - Ensure feedback template exists for the event
   - Check event permissions and status

3. **Analytics Not Updating**:
   - Verify feedback responses are being saved
   - Check organizer permissions

### Monitoring

- Monitor email delivery rates in Resend dashboard
- Track feedback response rates in analytics
- Monitor API endpoint performance
- Set up alerts for failed email deliveries

## Future Enhancements

- **Advanced Analytics**: Sentiment analysis, word clouds
- **Email Templates**: Multiple template options
- **Feedback Reminders**: Follow-up emails for non-responders
- **Integration**: Connect with other analytics platforms
- **Mobile App**: Native mobile feedback experience
- **Real-time Notifications**: Instant alerts for new feedback

## Support

For technical support or questions about the feedback system:
1. Check this documentation
2. Review the code comments
3. Check the API endpoint responses for error details
4. Monitor the application logs for debugging information
